# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Suna is an open-source generalist AI agent platform that helps users accomplish real-world tasks through natural conversation. It consists of a Python FastAPI backend and Next.js frontend.

## Common Development Commands

### Backend (Python)
```bash
# Navigate to backend
cd backend

# Install dependencies
uv sync --locked

# Run development server
uv run api.py

# Run background worker
uv run dramatiq --processes 4 --threads 4 run_agent_background

# Run tests
uv run pytest

# Docker infrastructure
docker compose up redis rabbitmq      # Just infrastructure services
docker compose up api worker          # Backend services
docker compose down && docker compose up --build  # Full rebuild

# Feature flags
cd backend/flags
python setup.py enable <flag_name> "Description"
python setup.py disable <flag_name>
python setup.py list
```

### Frontend (Next.js)
```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Development server
npm run dev

# Build production
npm run build

# Run production
npm run start

# Code quality
npm run lint          # ESLint
npm run format        # Prettier (write)
npm run format:check  # Prettier (check only)
```

### Full Stack
```bash
# Automated setup wizard (from root)
python setup.py

# Start entire stack
python start.py
```

## Architecture Overview

### Technology Stack
- **Backend**: Python 3.11, FastAPI, UV package manager
- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS v4
- **Database**: Supabase (PostgreSQL + Auth)
- **Queue**: RabbitMQ + Dramatiq
- **Cache**: Redis
- **Agent Environment**: Daytona SDK
- **Monitoring**: AgentOps, Langfuse, Sentry

### Key Directories
- `/backend`: FastAPI application
  - `/agent`: Agent execution logic
  - `/agentpress`: Core agent system
  - `/services`: External service integrations
  - `/workflows`: Workflow engine
  - `/scheduling`: Cron and QStash scheduling
  - `/webhooks`: Webhook handling
  - `/knowledge_base`: Document management
  - `/mcp_local`: Model Context Protocol support
  - `/utils`: Shared utilities
- `/frontend`: Next.js application
  - `/app`: App router pages and layouts
  - `/components`: React components
  - `/lib`: Utilities and hooks
  - `/public`: Static assets

### Core Systems

**Agent System**: Custom agents with versioning, marketplace sharing, tool integration (browser, files, shell, deploy), and MCP protocol support.

**Workflow Engine**: Visual workflow builder with scheduled execution, webhook triggers, and credential profiles.

**Tool Integration**: Browser automation (Playwright), file operations, code interpreter, API calls, and sandboxed execution.

**Authentication**: Supabase Auth with JWT tokens, team/organization support.

### Development Patterns
- Backend uses service-oriented architecture with clear separation of concerns
- Frontend uses component composition with custom hooks for data fetching
- All agent execution happens in isolated Daytona environments
- Background jobs processed via RabbitMQ/Dramatiq
- Real-time updates via Supabase subscriptions
- Feature flags managed through Redis
- Comprehensive error tracking with Sentry
- LLM observability with Langfuse and AgentOps