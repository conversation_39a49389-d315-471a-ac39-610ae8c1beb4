{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "mcp__knowledge-graph-memory__create_entities", "mcp__knowledge-graph-memory__create_relations", "mcp__think__think", "Bash(ls:*)", "<PERSON><PERSON>(poetry lock:*)", "Bash(docker-compose build:*)", "Bash(export COMPOSE_BAKE=true)", "Bash(node:*)", "Bash(docker-compose logs:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm install:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker-compose restart:*)", "Bash(grep:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(./test_initiate_direct.sh:*)", "Bash(docker logs:*)", "Bash(./build.sh)", "Bash(./build-fast-clean.sh)", "<PERSON><PERSON>(docker-compose up:*)", "mcp__exa__web_search_exa", "mcp__gitingest__git_summary", "mcp__gitingest__git_tree", "mcp__gitingest__git_files", "Bash(rg:*)", "mcp__firecrawl__firecrawl_scrape", "mcp__puppeteer__puppeteer_navigate", "Bash(rm:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_evaluate", "Bash(./fast-rebuild.sh)", "Bash(./rebuild.py)", "Bash(./rebuild.sh)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(md5sum:*)", "Bash(./rebuild.sh:*)", "<PERSON><PERSON>(docker inspect:*)"], "deny": []}}