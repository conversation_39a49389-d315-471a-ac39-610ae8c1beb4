"""
AgentOps integration service for tracking agent conversations and tool usage.
"""

import os
from typing import Optional, Any, Dict, List
import agentops
from agentops import TraceContext
from utils.logger import logger
import contextvars
import time
import asyncio

# Global variables for AgentOps state
_initialized = False

# Cache for conversation traces (thread_id -> TraceContext)
_conversation_traces: Dict[str, TraceContext] = {}

# Context variable for async trace propagation
agentops_trace_context: contextvars.ContextVar[Optional[TraceContext]] = contextvars.ContextVar(
    'agentops_trace_context', 
    default=None
)


class AgentSpanContext:
    """Context manager for agent run spans within a conversation trace."""
    def __init__(self, agent_run_id: str, model_name: str, agent_config: Optional[Dict[str, Any]] = None):
        self.agent_run_id = agent_run_id
        self.model_name = model_name
        self.agent_config = agent_config
        self.start_time = None
        
    async def __aenter__(self):
        self.start_time = time.time()
        
        # Record agent run start event
        metadata = {
            "agent_run_id": self.agent_run_id,
            "model": self.model_name,
            "agent_name": self.agent_config.get('name', 'default') if self.agent_config else 'default',
            "agent_id": self.agent_config.get('agent_id') if self.agent_config else None,
        }
        
        record_event(
            name="agent_run_start",
            level="DEFAULT",
            message=f"Starting agent run {self.agent_run_id}",
            metadata=metadata
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000 if self.start_time else 0
        
        if exc_type:
            record_event(
                name="agent_run_error",
                level="ERROR",
                message=f"Agent run failed: {self.agent_run_id}",
                metadata={
                    "agent_run_id": self.agent_run_id,
                    "error": str(exc_val),
                    "duration_ms": duration_ms
                }
            )
        else:
            record_event(
                name="agent_run_complete",
                level="DEFAULT",
                message=f"Agent run completed: {self.agent_run_id}",
                metadata={
                    "agent_run_id": self.agent_run_id,
                    "duration_ms": duration_ms
                }
            )


class ToolSpanContext:
    """Context manager for tool spans using AgentOps events."""
    def __init__(self, tool_name: str, tool_args: Optional[Dict[str, Any]] = None):
        self.tool_name = tool_name
        self.tool_args = tool_args or {}
        self.start_time = None
        
    async def __aenter__(self):
        self.start_time = time.time()
        record_event(
            name=f"tool_{self.tool_name}_start",
            level="DEFAULT",
            message=f"Starting tool execution: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "tool_args": self.tool_args
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000 if self.start_time else 0
        
        if exc_type:
            record_event(
                name=f"tool_{self.tool_name}_error",
                level="ERROR",
                message=f"Tool execution failed: {self.tool_name}",
                metadata={
                    "tool_name": self.tool_name,
                    "error": str(exc_val),
                    "duration_ms": duration_ms
                }
            )
        else:
            record_event(
                name=f"tool_{self.tool_name}_complete",
                level="DEFAULT",
                message=f"Tool execution completed: {self.tool_name}",
                metadata={
                    "tool_name": self.tool_name,
                    "duration_ms": duration_ms
                }
            )
    
    def record_result(self, result):
        """Record tool result for compatibility."""
        record_event(
            name=f"tool_{self.tool_name}_result",
            level="DEFAULT",
            message=f"Tool result recorded: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "result": str(result)[:1000]  # Limit result size
            }
        )
    
    def record_error(self, error):
        """Record tool error for compatibility."""
        record_event(
            name=f"tool_{self.tool_name}_error",
            level="ERROR",
            message=f"Tool error recorded: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "error": str(error)
            }
        )


class LLMSpanContext:
    """Context manager for LLM spans using AgentOps events."""
    def __init__(self, model: str, messages: List[Dict[str, Any]], temperature: float = 0.0):
        self.model = model
        self.messages = messages
        self.temperature = temperature
        self.start_time = None
        
    async def __aenter__(self):
        self.start_time = time.time()
        record_event(
            name="llm_call_start",
            level="DEFAULT",
            message=f"Starting LLM call with model: {self.model}",
            metadata={
                "model": self.model,
                "message_count": len(self.messages),
                "temperature": self.temperature
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000 if self.start_time else 0
        
        if exc_type:
            record_event(
                name="llm_call_error",
                level="ERROR",
                message=f"LLM call failed: {self.model}",
                metadata={
                    "model": self.model,
                    "error": str(exc_val),
                    "duration_ms": duration_ms
                }
            )
        else:
            record_event(
                name="llm_call_complete",
                level="DEFAULT",
                message=f"LLM call completed: {self.model}",
                metadata={
                    "model": self.model,
                    "duration_ms": duration_ms
                }
            )
    
    def record_response(self, response):
        """Record LLM response for compatibility."""
        metadata = {
            "model": self.model,
        }
        
        # Extract response details
        if hasattr(response, "choices") and response.choices:
            choice = response.choices[0]
            if hasattr(choice, "finish_reason"):
                metadata["finish_reason"] = choice.finish_reason
            if hasattr(choice, "message") and hasattr(choice.message, "content"):
                content = choice.message.content
                if content:
                    metadata["response_preview"] = str(content)[:200]
        
        # Add usage data
        if hasattr(response, "usage"):
            metadata["prompt_tokens"] = response.usage.prompt_tokens
            metadata["completion_tokens"] = response.usage.completion_tokens
            metadata["total_tokens"] = response.usage.prompt_tokens + response.usage.completion_tokens
        
        record_event(
            name="llm_response",
            level="DEFAULT",
            message=f"LLM response received from: {self.model}",
            metadata=metadata
        )


def initialize_agentops():
    """Initialize AgentOps SDK with configuration from environment variables."""
    global _initialized
    
    if _initialized:
        logger.debug("AgentOps already initialized")
        return
    
    api_key = os.getenv("AGENTOPS_API_KEY")
    if not api_key:
        logger.warning("AGENTOPS_API_KEY not found in environment variables. AgentOps will not be initialized.")
        return
    
    log_level = os.getenv("AGENTOPS_LOG_LEVEL", "INFO")
    
    try:
        agentops.init(
            api_key=api_key,
            log_level=log_level,
            instrument_llm_calls=False,
            auto_start_session=False,
            fail_safe=True,
        )
        _initialized = True
        logger.info("AgentOps initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize AgentOps: {str(e)}")
        _initialized = False


def start_agent_trace(
    agent_run_id: str,
    thread_id: str,
    project_id: str,
    model_name: str,
    agent_config: Optional[Dict[str, Any]] = None
) -> Optional[AgentSpanContext]:
    """
    Start an agent run span within the conversation trace.
    
    This creates or retrieves the conversation trace for the thread,
    then returns an AgentSpanContext that tracks this specific agent run
    as a span within that conversation.
    
    Args:
        agent_run_id: Unique identifier for the agent run
        thread_id: Thread ID for the conversation
        project_id: Project ID
        model_name: Name of the LLM model being used
        agent_config: Optional agent configuration
        
    Returns:
        AgentSpanContext object if successful, None otherwise
    """
    if not _initialized:
        logger.debug("AgentOps not initialized, skipping trace creation")
        return None
    
    try:
        # Get or create conversation trace
        conversation_trace = get_or_create_conversation_trace(thread_id, project_id, model_name)
        if not conversation_trace:
            logger.error("Failed to get conversation trace")
            return None
        
        # Set the conversation trace as current context
        agentops_trace_context.set(conversation_trace)
        
        # Create and return an agent span context
        # This represents a span within the conversation trace
        agent_span = AgentSpanContext(
            agent_run_id=agent_run_id,
            model_name=model_name,
            agent_config=agent_config
        )
        
        logger.info(f"Started agent run span {agent_run_id} within conversation {thread_id}")
        
        return agent_span
        
    except Exception as e:
        logger.error(f"Failed to start agent run: {str(e)}")
        return None


def end_agent_trace(
    agent_span: Optional[AgentSpanContext] = None,
    status: str = "completed",
    error: Optional[str] = None
) -> None:
    """
    End an agent run span (but not the conversation trace).
    
    Args:
        agent_span: The agent span context to end
        status: Final status of the agent run (completed, failed, stopped)
        error: Error message if status is failed
    """
    if not _initialized:
        return
    
    # Get current trace context
    context = agentops_trace_context.get()
    if not context:
        logger.debug("No active AgentOps trace context")
        return
    
    try:
        # Log the agent run completion
        # Note: We don't end the conversation trace here as it should persist
        # across multiple agent runs
        logger.info(f"Agent run ended with status: {status}")
        
        # Record completion event
        metadata = {"status": status}
        if agent_span:
            metadata["agent_run_id"] = agent_span.agent_run_id
            
        if error:
            record_event(
                name="agent_run_error",
                level="ERROR",
                message=error,
                metadata=metadata
            )
        else:
            record_event(
                name="agent_run_end",
                level="DEFAULT",
                message=f"Agent run ended with status: {status}",
                metadata=metadata
            )
        
    except Exception as e:
        logger.error(f"Failed to end agent run: {str(e)}")


def tool_span(tool_name: str, tool_args: Optional[Dict[str, Any]] = None):
    """Create a context manager for tool execution spans using AgentOps events."""
    return ToolSpanContext(tool_name, tool_args)


def llm_span(model: str, messages: List[Dict[str, Any]], temperature: float = 0.0):
    """Create a context manager for LLM call spans using AgentOps events."""
    return LLMSpanContext(model, messages, temperature)


def get_current_trace_context() -> Optional[TraceContext]:
    """Get the current active trace context."""
    return agentops_trace_context.get()


def is_initialized() -> bool:
    """Check if AgentOps is initialized."""
    return _initialized


def record_event(name: str, level: str = "DEFAULT", message: str = "", metadata: Optional[Dict[str, Any]] = None):
    """
    Record an event in AgentOps if a trace context is available.
    
    This function records events in the current trace context.
    
    Args:
        name: Event name (e.g., "billing_limit_reached")
        level: Event level (DEFAULT, WARNING, ERROR, CRITICAL)
        message: Event message/status message
        metadata: Optional metadata dictionary
    """
    # Get trace context from async context
    trace_context = agentops_trace_context.get()
    
    if not _initialized or not trace_context:
        return
    
    try:
        # Use the public API to record events
        event_data = {
            "name": name,
            "level": level,
            "message": message,
        }
        
        if metadata:
            event_data["metadata"] = metadata
            
        # Record the event using the public API
        agentops.record_event(**event_data)
        
        logger.debug(f"Recorded AgentOps event: {name} [{level}]")
            
    except Exception as e:
        logger.error(f"Failed to record AgentOps event '{name}': {str(e)}")


async def flush_trace() -> None:
    """
    Flush any pending data to ensure it's sent.
    """
    if not _initialized:
        return
    
    try:
        # Small delay to ensure data is sent
        await asyncio.sleep(0.1)
        logger.debug("Flushed pending data")
            
    except Exception as e:
        logger.error(f"Failed to flush trace: {str(e)}")


def get_or_create_conversation_trace(
    thread_id: str,
    project_id: str,
    model_name: str
) -> Optional[TraceContext]:
    """
    Get existing conversation trace or create a new one.
    
    This ensures that a single trace represents the entire conversation
    across multiple agent runs.
    
    Args:
        thread_id: Thread ID for the conversation
        project_id: Project ID
        model_name: Name of the LLM model being used
        
    Returns:
        TraceContext object if successful, None otherwise
    """
    global _conversation_traces
    
    if not _initialized:
        logger.debug("AgentOps not initialized, skipping trace creation")
        return None
    
    # Check if we already have a trace for this thread
    if thread_id in _conversation_traces:
        trace_context = _conversation_traces[thread_id]
        logger.debug(f"Using existing conversation trace for thread {thread_id}")
        
        # Set in async context for propagation
        agentops_trace_context.set(trace_context)
        return trace_context
    
    try:
        # Create new conversation trace
        trace_name = f"conversation_{thread_id}"
        tags = {
            "thread_id": thread_id,
            "project_id": project_id,
            "model": model_name,
            "conversation_id": thread_id
        }
        
        # Start the trace using the public API
        trace_context = agentops.start_trace(trace_name=trace_name, tags=tags)
        if trace_context:
            _conversation_traces[thread_id] = trace_context
            
            # Set in async context for propagation
            agentops_trace_context.set(trace_context)
            
            logger.info(f"Started new conversation trace for thread {thread_id}")
            
            # Record conversation start event
            record_event(
                name="conversation_start",
                level="DEFAULT",
                message=f"Started conversation in thread {thread_id}",
                metadata={
                    "thread_id": thread_id,
                    "project_id": project_id,
                    "model": model_name
                }
            )
        
        return trace_context
        
    except Exception as e:
        logger.error(f"Failed to create conversation trace: {str(e)}")
        return None


def end_conversation_trace(thread_id: str) -> None:
    """
    End a conversation trace and remove it from cache.
    
    This should only be called when the entire conversation is complete,
    not after each agent run.
    
    Args:
        thread_id: Thread ID for the conversation
    """
    global _conversation_traces
    
    if not _initialized:
        return
    
    if thread_id not in _conversation_traces:
        logger.debug(f"No conversation trace found for thread {thread_id}")
        return
    
    try:
        trace_context = _conversation_traces[thread_id]
        
        # Record conversation end event
        record_event(
            name="conversation_end",
            level="DEFAULT",
            message=f"Ended conversation in thread {thread_id}",
            metadata={"thread_id": thread_id}
        )
        
        # End the trace using the public API
        agentops.end_trace(trace_context=trace_context, end_state=agentops.SUCCESS)
        
        # Remove from cache
        del _conversation_traces[thread_id]
        
        # Clear async context if it matches
        if agentops_trace_context.get() == trace_context:
            agentops_trace_context.set(None)
            
        logger.info(f"Ended conversation trace for thread {thread_id}")
        
    except Exception as e:
        logger.error(f"Failed to end conversation trace: {str(e)}")