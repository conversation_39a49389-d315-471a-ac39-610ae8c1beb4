"""
AgentOps integration service for tracking agent conversations and tool usage.
Updated to use AgentOps SDK v4 with modern APIs and semantic conventions.
"""

import os
from typing import Optional, Any, Dict, List, TYPE_CHECKING
import agentops
from agentops import tracer, TraceState
from agentops.semconv import SpanAttributes, SpanKind, AgentAttributes, ToolAttributes
from utils.logger import logger
import contextvars
import time
import asyncio

if TYPE_CHECKING:
    from agentops.sdk.core import TraceContext

# Global variables for AgentOps state
_initialized = False

# Cache for conversation traces (thread_id -> TraceContext)
_conversation_traces: Dict[str, Any] = {}

# Context variable for async trace propagation
agentops_trace_context: contextvars.ContextVar[Optional[Any]] = contextvars.ContextVar(
    'agentops_trace_context',
    default=None
)


# Modern AgentOps integration using SDK v4 patterns
# The custom context managers are replaced with decorator-based approaches

def create_agent_span(agent_run_id: str, model_name: str, agent_config: Optional[Dict[str, Any]] = None):
    """
    Create an agent span using modern AgentOps decorators.
    This replaces the old AgentSpanContext class.
    """
    @agentops.agent
    async def agent_execution():
        # Set span attributes using AgentOps semantic conventions
        if tracer.initialized:
            current_span = tracer.get_current_span()
            if current_span:
                # Use AgentOps semantic conventions
                current_span.set_attribute(SpanAttributes.LLM_SYSTEM, "anthropic")
                current_span.set_attribute(SpanAttributes.OPERATION_NAME, "invoke_agent")
                current_span.set_attribute(SpanAttributes.LLM_REQUEST_MODEL, model_name)
                current_span.set_attribute(SpanAttributes.AGENTOPS_ENTITY_NAME,
                                         agent_config.get('name', 'default') if agent_config else 'default')
                current_span.set_attribute("agent.run_id", agent_run_id)

                if agent_config:
                    current_span.set_attribute(AgentAttributes.AGENT_ID, agent_config.get('agent_id', 'unknown'))
                    current_span.set_attribute(AgentAttributes.AGENT_NAME, agent_config.get('name', 'default'))

        # Record agent configuration event
        record_event(
            name="agent_configured",
            level="DEFAULT",
            message=f"Agent configured: {agent_config.get('name', 'default') if agent_config else 'default'}",
            metadata={
                "agent_run_id": agent_run_id,
                "model": model_name,
                "agent_name": agent_config.get('name', 'default') if agent_config else 'default',
                "agent_id": agent_config.get('agent_id') if agent_config else None,
            }
        )

        return agent_run_id

    return agent_execution


def instrument_tool_function(tool_name: str):
    """
    Decorator to instrument tool functions with AgentOps tracing.
    This provides modern AgentOps instrumentation for individual tool methods.
    """
    def decorator(func):
        @agentops.tool
        async def wrapper(*args, **kwargs):
            # Set span attributes using AgentOps semantic conventions
            if tracer.initialized:
                current_span = tracer.get_current_span()
                if current_span:
                    # Use AgentOps semantic conventions for tools
                    current_span.set_attribute(SpanAttributes.OPERATION_NAME, "execute_tool")
                    current_span.set_attribute(ToolAttributes.TOOL_NAME, tool_name)
                    current_span.set_attribute(SpanAttributes.AGENTOPS_ENTITY_NAME, tool_name)
                    if kwargs:
                        current_span.set_attribute(ToolAttributes.TOOL_PARAMETERS, str(kwargs))

            # Record tool execution start event
            record_event(
                name="tool_execution_start",
                level="DEFAULT",
                message=f"Starting tool execution: {tool_name}",
                metadata={
                    "tool_name": tool_name,
                    "tool_args": kwargs
                }
            )

            try:
                # Execute the actual tool function
                result = await func(*args, **kwargs)

                # Set success attributes
                if tracer.initialized:
                    current_span = tracer.get_current_span()
                    if current_span:
                        current_span.set_attribute(ToolAttributes.TOOL_STATUS, "success" if result.success else "failed")
                        current_span.set_attribute(ToolAttributes.TOOL_RESULT, str(result.output)[:1000])

                # Record tool execution success event
                record_event(
                    name="tool_execution_complete",
                    level="DEFAULT",
                    message=f"Tool execution completed: {tool_name}",
                    metadata={
                        "tool_name": tool_name,
                        "success": result.success,
                        "output_preview": str(result.output)[:200]
                    }
                )

                return result

            except Exception as e:
                # Set error attributes
                if tracer.initialized:
                    current_span = tracer.get_current_span()
                    if current_span:
                        current_span.set_attribute(ToolAttributes.TOOL_STATUS, "error")
                        current_span.set_attribute(ToolAttributes.TOOL_RESULT, f"Error: {str(e)}")

                # Record tool execution error event
                record_event(
                    name="tool_execution_error",
                    level="ERROR",
                    message=f"Tool execution failed: {tool_name}",
                    metadata={
                        "tool_name": tool_name,
                        "error": str(e),
                        "error_type": type(e).__name__
                    }
                )

                raise

        return wrapper
    return decorator


def create_tool_span(tool_name: str, tool_args: Optional[Dict[str, Any]] = None):
    """
    Create a tool span using modern AgentOps decorators.
    This replaces the old ToolSpanContext class.
    """
    @agentops.tool
    async def tool_execution():
        # Set span attributes using AgentOps semantic conventions
        if tracer.initialized:
            current_span = tracer.get_current_span()
            if current_span:
                # Use AgentOps semantic conventions for tools
                current_span.set_attribute(SpanAttributes.OPERATION_NAME, "execute_tool")
                current_span.set_attribute(ToolAttributes.TOOL_NAME, tool_name)
                current_span.set_attribute(SpanAttributes.AGENTOPS_ENTITY_NAME, tool_name)
                if tool_args:
                    current_span.set_attribute(ToolAttributes.TOOL_PARAMETERS, str(tool_args))

        # Record tool execution event
        record_event(
            name="tool_execution",
            level="DEFAULT",
            message=f"Executing tool: {tool_name}",
            metadata={
                "tool_name": tool_name,
                "tool_args": tool_args or {}
            }
        )

        return tool_name

    return tool_execution


class ToolSpanContext:
    """Legacy context manager for backward compatibility."""
    def __init__(self, tool_name: str, tool_args: Optional[Dict[str, Any]] = None):
        self.tool_name = tool_name
        self.tool_args = tool_args or {}
        self.start_time = None

    async def __aenter__(self):
        self.start_time = time.time()
        record_event(
            name=f"tool_{self.tool_name}_start",
            level="DEFAULT",
            message=f"Starting tool execution: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "tool_args": self.tool_args
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000 if self.start_time else 0

        if exc_type:
            record_event(
                name=f"tool_{self.tool_name}_error",
                level="ERROR",
                message=f"Tool execution failed: {self.tool_name}",
                metadata={
                    "tool_name": self.tool_name,
                    "error": str(exc_val),
                    "duration_ms": duration_ms
                }
            )
        else:
            record_event(
                name=f"tool_{self.tool_name}_complete",
                level="DEFAULT",
                message=f"Tool execution completed: {self.tool_name}",
                metadata={
                    "tool_name": self.tool_name,
                    "duration_ms": duration_ms
                }
            )

    def record_result(self, result):
        """Record tool result for compatibility."""
        record_event(
            name=f"tool_{self.tool_name}_result",
            level="DEFAULT",
            message=f"Tool result recorded: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "result": str(result)[:1000]  # Limit result size
            }
        )

    def record_error(self, error):
        """Record tool error for compatibility."""
        record_event(
            name=f"tool_{self.tool_name}_error",
            level="ERROR",
            message=f"Tool error recorded: {self.tool_name}",
            metadata={
                "tool_name": self.tool_name,
                "error": str(error)
            }
        )


class LLMSpanContext:
    """Context manager for LLM spans using AgentOps events."""
    def __init__(self, model: str, messages: List[Dict[str, Any]], temperature: float = 0.0):
        self.model = model
        self.messages = messages
        self.temperature = temperature
        self.start_time = None
        
    async def __aenter__(self):
        self.start_time = time.time()
        record_event(
            name="llm_call_start",
            level="DEFAULT",
            message=f"Starting LLM call with model: {self.model}",
            metadata={
                "model": self.model,
                "message_count": len(self.messages),
                "temperature": self.temperature
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000 if self.start_time else 0
        
        if exc_type:
            record_event(
                name="llm_call_error",
                level="ERROR",
                message=f"LLM call failed: {self.model}",
                metadata={
                    "model": self.model,
                    "error": str(exc_val),
                    "duration_ms": duration_ms
                }
            )
        else:
            record_event(
                name="llm_call_complete",
                level="DEFAULT",
                message=f"LLM call completed: {self.model}",
                metadata={
                    "model": self.model,
                    "duration_ms": duration_ms
                }
            )
    
    def record_response(self, response):
        """Record LLM response for compatibility."""
        metadata = {
            "model": self.model,
        }
        
        # Extract response details
        if hasattr(response, "choices") and response.choices:
            choice = response.choices[0]
            if hasattr(choice, "finish_reason"):
                metadata["finish_reason"] = choice.finish_reason
            if hasattr(choice, "message") and hasattr(choice.message, "content"):
                content = choice.message.content
                if content:
                    metadata["response_preview"] = str(content)[:200]
        
        # Add usage data
        if hasattr(response, "usage"):
            metadata["prompt_tokens"] = response.usage.prompt_tokens
            metadata["completion_tokens"] = response.usage.completion_tokens
            metadata["total_tokens"] = response.usage.prompt_tokens + response.usage.completion_tokens
        
        record_event(
            name="llm_response",
            level="DEFAULT",
            message=f"LLM response received from: {self.model}",
            metadata=metadata
        )


def initialize_agentops():
    """Initialize AgentOps SDK with configuration from environment variables using modern v4 patterns."""
    global _initialized

    if _initialized:
        logger.debug("AgentOps already initialized")
        return

    api_key = os.getenv("AGENTOPS_API_KEY")
    if not api_key:
        logger.warning("AGENTOPS_API_KEY not found in environment variables. AgentOps will not be initialized.")
        return

    log_level = os.getenv("AGENTOPS_LOG_LEVEL", "INFO")
    endpoint = os.getenv("AGENTOPS_ENDPOINT")

    try:
        # Use modern v4 initialization with enhanced configuration
        agentops.init(
            api_key=api_key,
            log_level=log_level,
            instrument_llm_calls=True,  # Enable automatic LLM instrumentation
            auto_start_session=False,   # We'll manage sessions manually
            fail_safe=True,
            endpoint=endpoint,
            default_tags=["suna", "agent-platform"],
        )
        _initialized = True
        logger.info("AgentOps v4 initialized successfully with modern patterns")
    except Exception as e:
        logger.error(f"Failed to initialize AgentOps: {str(e)}")
        _initialized = False


def start_agent_trace(
    agent_run_id: str,
    thread_id: str,
    project_id: str,
    model_name: str,
    agent_config: Optional[Dict[str, Any]] = None
) -> Optional[Any]:
    """
    Start an agent run trace using modern AgentOps v4 patterns.

    This creates or retrieves the conversation trace for the thread,
    then starts an agent span within that conversation.

    Args:
        agent_run_id: Unique identifier for the agent run
        thread_id: Thread ID for the conversation
        project_id: Project ID
        model_name: Name of the LLM model being used
        agent_config: Optional agent configuration

    Returns:
        Trace context object if successful, None otherwise
    """
    if not _initialized:
        logger.debug("AgentOps not initialized, skipping trace creation")
        return None

    try:
        # Get or create conversation trace using modern API
        conversation_trace = get_or_create_conversation_trace(thread_id, project_id, model_name)
        if not conversation_trace:
            logger.error("Failed to get conversation trace")
            return None

        # Set the conversation trace as current context
        agentops_trace_context.set(conversation_trace)

        # Record agent run start with AgentOps semantic conventions
        if tracer.initialized:
            current_span = tracer.get_current_span()
            if current_span:
                # Use AgentOps semantic conventions
                current_span.set_attribute(SpanAttributes.LLM_SYSTEM, "anthropic")
                current_span.set_attribute(SpanAttributes.OPERATION_NAME, "invoke_agent")
                current_span.set_attribute(SpanAttributes.LLM_REQUEST_MODEL, model_name)
                current_span.set_attribute(SpanAttributes.AGENTOPS_ENTITY_NAME,
                                         agent_config.get('name', 'default') if agent_config else 'default')
                current_span.set_attribute("agent.run_id", agent_run_id)
                current_span.set_attribute("agent.thread_id", thread_id)
                current_span.set_attribute("agent.project_id", project_id)

                if agent_config:
                    current_span.set_attribute(AgentAttributes.AGENT_ID, agent_config.get('agent_id', 'unknown'))
                    current_span.set_attribute(AgentAttributes.AGENT_NAME, agent_config.get('name', 'default'))

        # Record agent run start event
        record_event(
            name="agent_run_start",
            level="DEFAULT",
            message=f"Starting agent run {agent_run_id}",
            metadata={
                "agent_run_id": agent_run_id,
                "thread_id": thread_id,
                "project_id": project_id,
                "model": model_name,
                "agent_name": agent_config.get('name', 'default') if agent_config else 'default',
                "agent_id": agent_config.get('agent_id') if agent_config else None,
            }
        )

        logger.info(f"Started agent run trace {agent_run_id} within conversation {thread_id}")

        return conversation_trace

    except Exception as e:
        logger.error(f"Failed to start agent run: {str(e)}")
        return None


def end_agent_trace(
    agent_span: Optional[Any] = None,
    status: str = "completed",
    error: Optional[str] = None
) -> None:
    """
    End an agent run (but not the conversation trace).

    Args:
        agent_span: The agent span context to end (legacy parameter)
        status: Final status of the agent run (completed, failed, stopped)
        error: Error message if status is failed
    """
    if not _initialized:
        return

    # Get current trace context
    context = agentops_trace_context.get()
    if not context:
        logger.debug("No active AgentOps trace context")
        return

    try:
        # Set span status using semantic conventions
        if tracer.initialized:
            current_span = tracer.get_current_span()
            if current_span:
                if error:
                    current_span.set_status(agentops.TraceState.ERROR, error)
                else:
                    current_span.set_status(agentops.TraceState.SUCCESS)

                # Use semantic conventions for completion status
                current_span.set_attribute("gen_ai.agent.completion_status", status)

        # Record completion event
        metadata = {"status": status}

        if error:
            record_event(
                name="agent_run_error",
                level="ERROR",
                message=error,
                metadata=metadata
            )
        else:
            record_event(
                name="agent_run_end",
                level="DEFAULT",
                message=f"Agent run ended with status: {status}",
                metadata=metadata
            )

        logger.info(f"Agent run ended with status: {status}")

    except Exception as e:
        logger.error(f"Failed to end agent run: {str(e)}")


def tool_span(tool_name: str, tool_args: Optional[Dict[str, Any]] = None):
    """Create a context manager for tool execution spans using AgentOps events."""
    return ToolSpanContext(tool_name, tool_args)


def llm_span(model: str, messages: List[Dict[str, Any]], temperature: float = 0.0):
    """Create a context manager for LLM call spans using AgentOps events."""
    return LLMSpanContext(model, messages, temperature)


def get_current_trace_context() -> Optional[TraceContext]:
    """Get the current active trace context."""
    return agentops_trace_context.get()


def is_initialized() -> bool:
    """Check if AgentOps is initialized."""
    return _initialized


def record_event(name: str, level: str = "DEFAULT", message: str = "", metadata: Optional[Dict[str, Any]] = None):
    """
    Record an event in AgentOps if a trace context is available.
    
    This function records events in the current trace context.
    
    Args:
        name: Event name (e.g., "billing_limit_reached")
        level: Event level (DEFAULT, WARNING, ERROR, CRITICAL)
        message: Event message/status message
        metadata: Optional metadata dictionary
    """
    # Get trace context from async context
    trace_context = agentops_trace_context.get()
    
    if not _initialized or not trace_context:
        return
    
    try:
        # Use the public API to record events
        event_data = {
            "name": name,
            "level": level,
            "message": message,
        }
        
        if metadata:
            event_data["metadata"] = metadata
            
        # Record the event using the public API
        agentops.record_event(**event_data)
        
        logger.debug(f"Recorded AgentOps event: {name} [{level}]")
            
    except Exception as e:
        logger.error(f"Failed to record AgentOps event '{name}': {str(e)}")


async def flush_trace() -> None:
    """
    Flush any pending data to ensure it's sent.
    """
    if not _initialized:
        return
    
    try:
        # Small delay to ensure data is sent
        await asyncio.sleep(0.1)
        logger.debug("Flushed pending data")
            
    except Exception as e:
        logger.error(f"Failed to flush trace: {str(e)}")


def get_or_create_conversation_trace(
    thread_id: str,
    project_id: str,
    model_name: str
) -> Optional[Any]:
    """
    Get existing conversation trace or create a new one using modern AgentOps v4 patterns.

    This ensures that a single trace represents the entire conversation
    across multiple agent runs.

    Args:
        thread_id: Thread ID for the conversation
        project_id: Project ID
        model_name: Name of the LLM model being used

    Returns:
        Trace context object if successful, None otherwise
    """
    global _conversation_traces

    if not _initialized:
        logger.debug("AgentOps not initialized, skipping trace creation")
        return None

    # Check if we already have a trace for this thread
    if thread_id in _conversation_traces:
        trace_context = _conversation_traces[thread_id]
        logger.debug(f"Using existing conversation trace for thread {thread_id}")

        # Set in async context for propagation
        agentops_trace_context.set(trace_context)
        return trace_context

    try:
        # Create new conversation trace using modern v4 API
        trace_name = f"conversation_{thread_id}"
        metadata = {
            "thread_id": thread_id,
            "project_id": project_id,
            "model": model_name,
            "conversation_type": "agent_chat",
            "trace_version": "v4"
        }
        tags = ["conversation", "suna", project_id]

        # Start the trace using the modern public API
        trace_context = agentops.start_trace(
            name=trace_name,
            metadata=metadata,
            tags=tags
        )

        if trace_context:
            # Set semantic attributes on the trace using modern conventions
            if tracer.initialized:
                current_span = tracer.get_current_span()
                if current_span:
                    current_span.set_attribute(SpanAttributes.LLM_REQUEST_MODEL, model_name)
                    current_span.set_attribute("conversation.thread_id", thread_id)
                    current_span.set_attribute("conversation.project_id", project_id)
                    current_span.set_attribute("conversation.type", "agent_chat")
                    current_span.set_attribute("conversation.version", "v4")

            _conversation_traces[thread_id] = trace_context

            # Set in async context for propagation
            agentops_trace_context.set(trace_context)

            logger.info(f"Started new conversation trace for thread {thread_id} using v4 patterns")

            # Record conversation start event with enhanced metadata
            record_event(
                name="conversation_start",
                level="DEFAULT",
                message=f"Started conversation in thread {thread_id}",
                metadata={
                    "thread_id": thread_id,
                    "project_id": project_id,
                    "model": model_name,
                    "conversation_type": "agent_chat",
                    "sdk_version": "v4"
                }
            )

        return trace_context

    except Exception as e:
        logger.error(f"Failed to create conversation trace: {str(e)}")
        return None


def end_conversation_trace(thread_id: str) -> None:
    """
    End a conversation trace and remove it from cache.
    
    This should only be called when the entire conversation is complete,
    not after each agent run.
    
    Args:
        thread_id: Thread ID for the conversation
    """
    global _conversation_traces
    
    if not _initialized:
        return
    
    if thread_id not in _conversation_traces:
        logger.debug(f"No conversation trace found for thread {thread_id}")
        return
    
    try:
        trace_context = _conversation_traces[thread_id]
        
        # Record conversation end event
        record_event(
            name="conversation_end",
            level="DEFAULT",
            message=f"Ended conversation in thread {thread_id}",
            metadata={"thread_id": thread_id}
        )
        
        # End the trace using the public API
        agentops.end_trace(trace_context=trace_context, end_state=agentops.SUCCESS)
        
        # Remove from cache
        del _conversation_traces[thread_id]
        
        # Clear async context if it matches
        if agentops_trace_context.get() == trace_context:
            agentops_trace_context.set(None)
            
        logger.info(f"Ended conversation trace for thread {thread_id}")
        
    except Exception as e:
        logger.error(f"Failed to end conversation trace: {str(e)}")