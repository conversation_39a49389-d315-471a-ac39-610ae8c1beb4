"""
Comprehensive tests for AgentOps integration with Suna.

This test suite verifies that the AgentOps SDK v4 integration works correctly
with modern semantic conventions, decorators, and tracing functionality.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

# Import the modules we're testing
from services.agentops import (
    init_agentops,
    record_event,
    get_current_trace_context,
    is_initialized,
    instrument_tool_function,
    create_tool_span,
    ToolSpanContext,
    LLMSpanContext
)
from services.llm import make_llm_api_call, _make_instrumented_llm_call
from agentpress.response_processor import ResponseProcessor
from agentpress.tool_registry import ToolRegistry
from agentpress.tool import Tool, ToolResult


class MockTool(Tool):
    """Mock tool for testing purposes."""
    
    async def test_function(self, message: str = "test") -> ToolResult:
        """Test function that returns a simple result."""
        return ToolResult(success=True, output=f"Mock tool executed with: {message}")
    
    async def failing_function(self) -> ToolResult:
        """Test function that fails."""
        raise Exception("Mock tool failure")


@pytest.fixture
def mock_agentops():
    """Mock AgentOps tracer and related functionality."""
    with patch('agentops.tracer') as mock_tracer, \
         patch('agentops.tool') as mock_tool_decorator, \
         patch('agentops.trace') as mock_trace_decorator:
        
        # Setup mock tracer
        mock_tracer.initialized = True
        mock_span = Mock()
        mock_tracer.get_current_span.return_value = mock_span
        
        # Setup mock decorators to pass through functions
        mock_tool_decorator.side_effect = lambda func: func
        mock_trace_decorator.side_effect = lambda **kwargs: lambda func: func
        
        yield {
            'tracer': mock_tracer,
            'span': mock_span,
            'tool_decorator': mock_tool_decorator,
            'trace_decorator': mock_trace_decorator
        }


@pytest.fixture
def tool_registry():
    """Create a tool registry with mock tools."""
    registry = ToolRegistry()
    mock_tool = MockTool()
    registry.register_tool(MockTool, ["test_function", "failing_function"])
    return registry


@pytest.fixture
def response_processor(tool_registry):
    """Create a response processor with mock dependencies."""
    with patch('agentpress.response_processor.logger') as mock_logger:
        processor = ResponseProcessor(
            tool_registry=tool_registry,
            trace=Mock(),  # Mock Langfuse trace
            conversation_id="test-conversation",
            user_id="test-user"
        )
        yield processor


class TestAgentOpsInitialization:
    """Test AgentOps initialization and configuration."""
    
    @patch('agentops.init')
    def test_init_agentops_with_default_config(self, mock_init):
        """Test AgentOps initialization with default configuration."""
        mock_init.return_value = True
        
        result = init_agentops()
        
        assert result is True
        mock_init.assert_called_once()
        call_kwargs = mock_init.call_args[1]
        assert call_kwargs['instrument_llm_calls'] is True
        assert 'api_key' in call_kwargs
    
    @patch('agentops.init')
    def test_init_agentops_with_custom_config(self, mock_init):
        """Test AgentOps initialization with custom configuration."""
        mock_init.return_value = True
        
        result = init_agentops(
            api_key="test-key",
            tags=["test", "integration"],
            instrument_llm_calls=False
        )
        
        assert result is True
        mock_init.assert_called_once()
        call_kwargs = mock_init.call_args[1]
        assert call_kwargs['api_key'] == "test-key"
        assert call_kwargs['tags'] == ["test", "integration"]
        assert call_kwargs['instrument_llm_calls'] is False


class TestAgentOpsEventRecording:
    """Test AgentOps event recording functionality."""
    
    @patch('agentops.record')
    def test_record_event_basic(self, mock_record):
        """Test basic event recording."""
        record_event(
            name="test_event",
            level="DEFAULT",
            message="Test message"
        )
        
        mock_record.assert_called_once()
        call_args = mock_record.call_args[0][0]
        assert call_args.event_type == "test_event"
        assert call_args.level == "DEFAULT"
        assert call_args.message == "Test message"
    
    @patch('agentops.record')
    def test_record_event_with_metadata(self, mock_record):
        """Test event recording with metadata."""
        metadata = {"key": "value", "count": 42}
        
        record_event(
            name="test_event_with_metadata",
            level="INFO",
            message="Test with metadata",
            metadata=metadata
        )
        
        mock_record.assert_called_once()
        call_args = mock_record.call_args[0][0]
        assert call_args.event_type == "test_event_with_metadata"
        assert call_args.metadata == metadata


class TestToolInstrumentation:
    """Test tool instrumentation with AgentOps decorators."""
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_agentops_decorator(self, mock_agentops, response_processor):
        """Test that tool execution uses AgentOps decorator correctly."""
        tool_call = {
            "function_name": "test_function",
            "arguments": {"message": "hello world"}
        }
        
        result = await response_processor._execute_tool(tool_call)
        
        assert result.success is True
        assert "hello world" in result.output
        
        # Verify AgentOps span attributes were set
        mock_span = mock_agentops['span']
        assert mock_span.set_attribute.called
        
        # Check that semantic conventions were used
        attribute_calls = [call[0] for call in mock_span.set_attribute.call_args_list]
        assert any("OPERATION_NAME" in str(call) for call in attribute_calls)
        assert any("TOOL_NAME" in str(call) for call in attribute_calls)
    
    @pytest.mark.asyncio
    async def test_tool_execution_error_handling(self, mock_agentops, response_processor):
        """Test that tool execution errors are properly recorded in AgentOps."""
        tool_call = {
            "function_name": "failing_function",
            "arguments": {}
        }
        
        result = await response_processor._execute_tool(tool_call)
        
        assert result.success is False
        assert "Mock tool failure" in result.output
        
        # Verify error attributes were set
        mock_span = mock_agentops['span']
        attribute_calls = [call[0] for call in mock_span.set_attribute.call_args_list]
        assert any("TOOL_STATUS" in str(call) for call in attribute_calls)
    
    def test_instrument_tool_function_decorator(self, mock_agentops):
        """Test the instrument_tool_function decorator."""
        @instrument_tool_function("test_tool")
        async def mock_tool_func(param1: str) -> ToolResult:
            return ToolResult(success=True, output=f"Result: {param1}")
        
        # Verify the decorator was applied
        assert callable(mock_tool_func)
        
        # The actual execution test would require more complex async setup
        # This test verifies the decorator can be applied without errors


class TestLLMInstrumentation:
    """Test LLM instrumentation with AgentOps."""
    
    @pytest.mark.asyncio
    async def test_llm_call_instrumentation(self, mock_agentops):
        """Test that LLM calls are properly instrumented with AgentOps."""
        mock_response = Mock()
        mock_response.model = "gpt-4"
        mock_response.id = "test-id"
        mock_response.usage = Mock()
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 20
        mock_response.usage.total_tokens = 30
        mock_response.choices = [Mock()]
        mock_response.choices[0].finish_reason = "stop"
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = "Test response"
        
        with patch('litellm.acompletion', return_value=mock_response):
            result = await _make_instrumented_llm_call(
                params={"model": "gpt-4", "messages": [{"role": "user", "content": "test"}]},
                model_name="gpt-4",
                messages=[{"role": "user", "content": "test"}],
                temperature=0.7
            )
        
        assert result == mock_response
        
        # Verify AgentOps span attributes were set
        mock_span = mock_agentops['span']
        assert mock_span.set_attribute.called
        
        # Check that LLM semantic conventions were used
        attribute_calls = [call[0] for call in mock_span.set_attribute.call_args_list]
        assert any("LLM_SYSTEM" in str(call) for call in attribute_calls)
        assert any("LLM_REQUEST_MODEL" in str(call) for call in attribute_calls)


class TestTraceContextManagement:
    """Test trace context management and propagation."""
    
    def test_get_current_trace_context(self, mock_agentops):
        """Test getting current trace context."""
        mock_context = {"trace_id": "test-trace-id", "span_id": "test-span-id"}
        
        with patch('services.agentops.get_current_trace_context', return_value=mock_context):
            context = get_current_trace_context()
            assert context == mock_context
    
    def test_is_initialized(self, mock_agentops):
        """Test AgentOps initialization status check."""
        assert is_initialized() is True
        
        mock_agentops['tracer'].initialized = False
        assert is_initialized() is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
